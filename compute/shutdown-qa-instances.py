import boto3
import re

def lambda_handler(event, context):

    ec2 = boto3.client('ec2')
    
    tag_pattern = r'^thankview-qa.*'
    
    response = ec2.describe_instances()
    
    instances_to_stop = []
    
    # Iterate through reservations and instances
    for reservation in response['Reservations']:
        for instance in reservation['Instances']:
            
            if 'Tags' in instance:
                for tag in instance['Tags']:
                    
                    if tag['Key'] == 'Name' and re.match(tag_pattern, tag['Value']):
                        instances_to_stop.append(instance['InstanceId'])
                        break  
    
    # Stop the matched instances
    if instances_to_stop:
        ec2.stop_instances(InstanceIds=instances_to_stop)
        print(f"Stopping instances: {instances_to_stop}")
    else:
        print("No instances found matching the tag pattern.")
    
    return {
        'statusCode': 200,
        'body': f"Attempted to stop {len(instances_to_stop)} instances."
    }