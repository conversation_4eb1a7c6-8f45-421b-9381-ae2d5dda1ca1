# Create a ZIP file of the Python script
data "archive_file" "lambda_zip" {
  type        = "zip"
  source_file = "${path.module}/shutdown-qa-instances.py"
  output_path = "${path.module}/stop_lambda_function.zip"
}

# Lambda function
resource "aws_lambda_function" "qa_ec2_shutdown" {
  filename         = data.archive_file.lambda_zip.output_path
  function_name    = "qa-ec2-shutdown-function"
  role             = aws_iam_role.lambda_role.arn
  handler          = "shutdown-qa-instances.lambda_handler"
  runtime          = "python3.8"
  source_code_hash = data.archive_file.lambda_zip.output_base64sha256

  environment {
    variables = {
      TAG_PATTERN = var.tag_pattern
    }
  }
}

# IAM role for Lambda
resource "aws_iam_role" "lambda_role" {
  name = "ec2-shutdown-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

# IAM policy for Lambda
resource "aws_iam_role_policy" "lambda_policy" {
  name = "ec2-shutdown-lambda-policy"
  role = aws_iam_role.lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ec2:DescribeInstances",
          "ec2:StopInstances"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      }
    ]
  })
}

# CloudWatch Log Group for Lambda
resource "aws_cloudwatch_log_group" "lambda_log_group" {
  name              = "/aws/lambda/${aws_lambda_function.qa_ec2_shutdown.function_name}"
  retention_in_days = 14
}