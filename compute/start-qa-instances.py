import boto3
import re

def lambda_handler(event, context):

    ec2 = boto3.client('ec2')
    
    tag_pattern = r'^thankview-qa.*'

    response = ec2.describe_instances()
    
    instances_to_start = []
    
    # Iterate through reservations and instances
    for reservation in response['Reservations']:
        for instance in reservation['Instances']:
            if 'Tags' in instance:
                for tag in instance['Tags']:
                    if tag['Key'] == 'Name' and re.match(tag_pattern, tag['Value']):
                        instances_to_start.append(instance['InstanceId'])
                        break 
    
    # Start the matched instances
    if instances_to_start:
        ec2.start_instances(InstanceIds=instances_to_start)
        print(f"Starting instances: {instances_to_start}")
    else:
        print("No instances found matching the tag pattern.")
    
    return {
        'statusCode': 200,
        'body': f"Attempted to start {len(instances_to_start)} instances."
    }