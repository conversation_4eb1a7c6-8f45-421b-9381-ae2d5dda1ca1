# Create a ZIP file of the Python script
data "archive_file" "start_lambda_zip" {
  type        = "zip"
  source_file = "${path.module}/start-qa-instances.py"
  output_path = "${path.module}/start_lambda_function.zip"
}

# Lambda function
resource "aws_lambda_function" "qa_ec2_startup" {
  filename         = data.archive_file.start_lambda_zip.output_path
  function_name    = "qa-ec2-startup-function"
  role             = aws_iam_role.lambda_role.arn
  handler          = "start-qa-instances.lambda_handler"
  runtime          = "python3.8"
  source_code_hash = data.archive_file.lambda_zip.output_base64sha256

  environment {
    variables = {
      TAG_PATTERN = var.tag_pattern
    }
  }
}

# IAM role for Lambda
resource "aws_iam_role" "start_lambda_role" {
  name = "ec2-start-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

# IAM policy for Lambda
resource "aws_iam_role_policy" "start_lambda_policy" {
  name = "ec2-start-lambda-policy"
  role = aws_iam_role.start_lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ec2:DescribeInstances",
          "ec2:StartInstances"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      }
    ]
  })
}

# CloudWatch Log Group for Lambda
resource "aws_cloudwatch_log_group" "start_lambda_log_group" {
  name              = "/aws/lambda/${aws_lambda_function.qa_ec2_startup.function_name}"
  retention_in_days = 14
}