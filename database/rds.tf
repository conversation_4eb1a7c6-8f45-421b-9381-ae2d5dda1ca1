locals {
  dbs = {
    dev = {
      # How soon to apply changes
      apply_immediately = true

      # License
      license_model = "license-included"

      # Engine Info
      engine         = "mysql"
      engine_version = "8.0"


      # Instance size and storage
      instance_class        = "db.t3.medium"
      allocated_storage     = 20
      max_allocated_storage = 40
      storage_encrypted     = true # NOTE: Encryption at rest is not available for DB instances running SQL Server Express Edition

      # DB Auth Settings
      username = "admin"
      # password is managed by AWS RDS via AWS Secrets Manager

      # Network info
      port                   = 3306
      multi_az               = false
      db_subnet_group_name   = default
      vpc_security_group_ids = ["${module.security_group["dev-self"].security_group_id}"]

      # Maint, backup, monitoring, logs
      maintenance_window      = "Mon:00:00-Mon:03:00"
      backup_window           = "03:00-06:00"
      backup_retention_period = 1
      skip_final_snapshot     = true

      # Database Deletion Protection
      deletion_protection = false


      # Options
      create_db_option_group = true
      major_engine_version   = "8.0"
      options = [
        {
          option_name = "SQLSERVER_BACKUP_RESTORE"
          option_settings = [
            {
              name  = "IAM_ROLE_ARN"
              value = "arn:aws:iam::331872043419:role/mssql_rds_role"
            },
          ]
        }
      ]

      # ...and parameters
      family                    = "mysql8.0" # DB parameter group
      create_db_parameter_group = false
      parameters                = []

      # Misc
      timezone           = "GMT Standard Time"
      character_set_name = "Latin1_General_CI_AS"

      tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-18778", Creator = "<EMAIL>", Tier = "Dev", Component = "RDS DB", CreateDate = "20230509" })}"
    }
  }
}

module "db" {
  source = "terraform-aws-modules/rds/aws"

  for_each = local.dbs

  # How soon to apply changes
  apply_immediately = try(each.value.apply_immediately, false)

  # License
  license_model = try(each.value.license_model, "license-included")

  # Identity
  identifier = each.key
  db_name    = try(each.value.db_name, "")

  # Engine Info
  engine                      = try(each.value.engine, "")
  engine_version              = try(each.value.engine_version, "")
  major_engine_version        = try(each.value.major_engine_version, "")
  allow_major_version_upgrade = try(each.value.allow_major_version_upgrade, false)

  # Instance size and storage
  instance_class        = try(each.value.instance_class, "")
  allocated_storage     = try(each.value.allocated_storage, 5)
  max_allocated_storage = try(each.value.max_allocated_storage, 10)
  storage_encrypted     = try(each.value.storage_encrypted, true)

  # DB Auth Settings
  manage_master_user_password         = true
  username                            = try(each.value.username, "")
  iam_database_authentication_enabled = try(each.value.iam_database_authentication_enabled, false)
  domain                              = try(each.value.domain, "")
  domain_iam_role_name                = try(each.value.domain_iam_role_name, "")

  # Network info
  port                   = try(each.value.port, "")
  multi_az               = try(each.value.multi_az, false)
  db_subnet_group_name   = try(each.value.db_subnet_group_name, "")
  create_db_subnet_group = false
  vpc_security_group_ids = try(each.value.vpc_security_group_ids, [])

  # Maint, backup, monitoring, logs
  maintenance_window              = try(each.value.maintenance_window, "")
  backup_window                   = try(each.value.backup_window, "")
  backup_retention_period         = try(each.value.backup_retention_period, 1)
  skip_final_snapshot             = try(each.value.skip_final_snapshot, true)
  monitoring_interval             = try(each.value.monitoring_interval, 0)
  monitoring_role_name            = try(each.value.monitoring_role_name, "")
  create_monitoring_role          = try(each.value.create_monitoring_role, false)
  enabled_cloudwatch_logs_exports = try(each.value.enabled_cloudwatch_logs_exports, [])
  create_cloudwatch_log_group     = try(each.value.create_cloudwatch_log_group, false)

  # Database Deletion Protection
  deletion_protection = true

  # Options and parameters
  family                    = try(each.value.family, "")
  create_db_parameter_group = try(each.value.create_db_parameter_group, false)
  parameters                = try(each.value.parameters, [])
  create_db_option_group    = try(each.value.create_db_option_group, false)
  options                   = try(each.value.options, [])

  # Misc
  timezone           = try(each.value.timezone, "")
  character_set_name = try(each.value.character_set_name, "")

  # Tags
  tags = try(each.value.tags, {})
}
