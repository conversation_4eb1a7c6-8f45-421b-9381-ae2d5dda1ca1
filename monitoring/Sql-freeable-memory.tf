resource "aws_cloudwatch_metric_alarm" "rds_freeable_memory" {
  for_each = toset(data.aws_db_instances.all.instance_identifiers)

  alarm_name          = "${each.value}-RDS-FreeableMemory"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "FreeableMemory"
  namespace           = "AWS/RDS"
  period              = "300"  # 5 minutes in seconds
  statistic           = "Average"
  
  # Set threshold for FreeableMemory to 1.5GB in bytes
  threshold           = 1 * 1000000000
  
  alarm_description   = "This alarm will trigger when freeable memory drops below 1.5GB for 5 minutes"
  alarm_actions       = [aws_sns_topic.rds_alarm.arn]

  dimensions = {
    DBInstanceIdentifier = each.value
  }

  tags = {
    "ManagedBy" = "Terraform"
    "Owner"     = "Michael D"
  }
}