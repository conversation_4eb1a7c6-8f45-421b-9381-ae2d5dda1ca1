data "aws_rds_clusters" "all" {}

# DiskQueueDepth
resource "aws_cloudwatch_metric_alarm" "rds_disk_queue_depth" {
  for_each = toset(data.aws_db_instances.all.instance_identifiers)

  alarm_name          = "${each.value}-RDS-DiskQueueDepth"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "DiskQueueDepth"
  namespace           = "AWS/RDS"
  period              = "300"
  statistic           = "Average"
  threshold           = 2000
  
  alarm_description   = "This alarm will trigger when DiskQueueDepth exceeds 2000 for 5 minutes"
  alarm_actions       = [aws_sns_topic.rds_alarm.arn]

  dimensions = {
    DBInstanceIdentifier = each.value
  }

  tags = {
    "ManagedBy" = "Terraform"
    "Owner"     = "Michael D"
  }
}

# ReadIOPS
resource "aws_cloudwatch_metric_alarm" "rds_read_iops" {
  for_each = toset(data.aws_db_instances.all.instance_identifiers)

  alarm_name          = "${each.value}-RDS-ReadIOPS"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "ReadIOPS"
  namespace           = "AWS/RDS"
  period              = "900"
  statistic           = "Average"
  threshold           = 1000
  
  alarm_description   = "This alarm will trigger when ReadIOPS exceeds 500 counts/second for 5 minutes"
  alarm_actions       = [aws_sns_topic.rds_alarm.arn]

  dimensions = {
    DBInstanceIdentifier = each.value
  }

  tags = {
    "ManagedBy" = "Terraform"
    "Owner"     = "Michael D"
  }
}

# WriteIOPS
resource "aws_cloudwatch_metric_alarm" "rds_write_iops" {
  for_each = toset(data.aws_db_instances.all.instance_identifiers)

  alarm_name          = "${each.value}-RDS-WriteIOPS"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "WriteIOPS"
  namespace           = "AWS/RDS"
  period              = "300"
  statistic           = "Average"
  threshold           = 250
  
  alarm_description   = "This alarm will trigger when WriteIOPS exceeds 250 counts/second for 5 minutes"
  alarm_actions       = [aws_sns_topic.rds_alarm.arn]

  dimensions = {
    DBInstanceIdentifier = each.value
  }

  tags = {
    "ManagedBy" = "Terraform"
    "Owner"     = "Michael D"
  }
}

# VolumeReadIOPs for clusters
resource "aws_cloudwatch_metric_alarm" "rds_cluster_volume_read_iops" {
  for_each = toset(data.aws_rds_clusters.all.cluster_identifiers)

  alarm_name          = "${each.value}-RDS-Cluster-VolumeReadIOPs"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "20"  # 5 minutes * 20 = 4 hours
  metric_name         = "VolumeReadIOPs"
  namespace           = "AWS/RDS"
  period              = "300"
  statistic           = "Average"
  threshold           = 100000
  
  alarm_description   = "This alarm will trigger when VolumeReadIOPs exceeds 1 count for more than 4 hours"
  alarm_actions       = [aws_sns_topic.rds_alarm.arn]

  dimensions = {
    DBClusterIdentifier = each.value
  }

  tags = {
    "ManagedBy" = "Terraform"
    "Owner"     = "Michael D"
  }
}
# VolumeWriteIOPs for clusters
resource "aws_cloudwatch_metric_alarm" "rds_cluster_volume_write_iops" {
  for_each = toset(data.aws_rds_clusters.all.cluster_identifiers)

  alarm_name          = "${each.value}-RDS-Cluster-VolumeWriteIOPs"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"  # 5 minutes * 3 = 15 minutes
  metric_name         = "VolumeWriteIOPs"
  namespace           = "AWS/RDS"
  period              = "300"
  statistic           = "Average"
  threshold           = 800
  
  alarm_description   = "This alarm will trigger when VolumeWriteIOPs exceeds 800 units for 15 minutes"
  alarm_actions       = [aws_sns_topic.rds_alarm.arn]

  dimensions = {
    DBClusterIdentifier = each.value
  }

  tags = {
    "ManagedBy" = "Terraform"
    "Owner"     = "Michael D"
  }
}
