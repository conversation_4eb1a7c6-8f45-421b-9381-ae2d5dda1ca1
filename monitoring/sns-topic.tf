data "aws_caller_identity" "current" {}

resource "aws_sns_topic" "rds_alarm" {
  name = "${var.account_name}-${data.aws_caller_identity.current.account_id}-RDS-alarm"

    tags = {
    "ManagedBy" = "Terraform"
    "Owner"     = "<PERSON>"
  }
}


resource "aws_sns_topic" "alarm_monitoring" {
  name              = "alarm-monitoring"
  display_name      = "Alarm Monitoring"
  fifo_topic        = false
}

output "sns_topic_arn" {
  description = "ARN of the SNS topic"
  value       = aws_sns_topic.alarm_monitoring.arn
}
