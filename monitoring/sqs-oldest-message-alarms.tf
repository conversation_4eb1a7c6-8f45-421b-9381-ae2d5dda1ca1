# Data source to get the existing SNS topic for SQS alarms
data "aws_sns_topic" "default_cloudwatch_alarms" {
  name = "Default_CloudWatch_Alarms_Topic"
}

# SQS CloudWatch Alarms for ApproximateAgeOfOldestMessage
resource "aws_cloudwatch_metric_alarm" "sqs_oldest_message_alarms" {
  for_each = var.sqs_queues

  alarm_name          = "SQS ${each.value.display_name} - Oldest Message"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "ApproximateAgeOfOldestMessage"
  namespace           = "AWS/SQS"
  period              = "300"  # 5 minutes in seconds
  statistic           = "Average"
  threshold           = 10000  # 10,000 seconds as shown in your console
  
  alarm_description   = "This alarm will trigger when ApproximateAgeOfOldestMessage > 10000 for 1 datapoints within 5 minutes"
  alarm_actions       = [data.aws_sns_topic.default_cloudwatch_alarms.arn]
  
  # Treat missing data as missing (as shown in your console)
  treat_missing_data = "missing"

  dimensions = {
    QueueName = each.value.queue_name
  }

  tags = {
    "ManagedBy" = "Terraform"
    "Owner"     = "Michael D"
  }
}
