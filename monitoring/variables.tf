variable "account_name" {
  description = "The name of the AWS account."
  type        = string
  default     = "thankview"  # You can set a default or leave it out to make it mandatory.
}

variable "cpu_utilization_threshold" {
  description = "The threshold for the RDS CPU Utilization alarm."
  type        = number
  default     = 85
}
#-----------

variable "lambda_functions" {
  description = "List of Lambda function names"
  type        = list(string)
  default     = [
    "prod_bw_dm_get_tasks",
    "prod-bw-dms-run-job",
    "DatadogIntegration-ForwarderStack-16P3A0-Forwarder-yXVY32kBbZXw",
    "prod-bw-dms-job-status",
    "DatadogIntegration-DatadogA-DatadogAPICallFunction-6o2EbTgT6lee",
    "serve-compressed-asset",
    "thankview-dp-docker-sidec-TerminateInstanceFunctio-1UWIA0XHWF2LE",
    "thankview-qa-bitbucket-api-HandleUpdateFunction-cs51IuSDZCvk",
    "check-brotli-support",
    "thankview-dp-docker-sidecar-LaunchInstanceFunction-CYMTNCJ90H0U",
    "Restart-EC2-on-Status-CheckFailure",
    "scale-down-send-servers"
  ]
}
