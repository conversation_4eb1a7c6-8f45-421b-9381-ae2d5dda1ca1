locals {
  thankview_records = {
    "ci-jenkins" = {
      name = "ci-jenkins",
      records = [
        "18.206.136.225"
      ],
      type = "A",
      ttl  = "3600"
    }
  }
}

module "records" {
  source  = "terraform-aws-modules/route53/aws//modules/records"
  version = "~> 2.0"

  for_each = local.thankview_records

  records   = [each.value]
  zone_name = module.zones["thankview.com"].route53_zone_name["thankview.com"]
}
