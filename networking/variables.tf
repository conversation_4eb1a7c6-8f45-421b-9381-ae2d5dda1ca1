#########shared-infra-stage-subnets########

variable "thankview_subnets" {
  description = "List of subnet IDs for the thankview environment"
  type        = list(string)
  default     = [
    "subnet-26d20c1b",  // Public Subnet (AZ3)
    "subnet-3b61245e",  // Public Subnet (AZ1)
    "subnet-9b1d37b0",  // Public Subnet (AZ2)
    "subnet-c48261b2",  // Public Subnet (AZ4)
    "subnet-aa55c7a6",  // Public Subnet (AZ5)
    "subnet-17a59f4e",  // Public Subnet (AZ6)
  ]
}