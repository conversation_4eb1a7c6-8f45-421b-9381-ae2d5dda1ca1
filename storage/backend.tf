################################################################################
# Backend Configuration
################################################################################
terraform {
  # S3 Backend is always in the Tools account
  backend "s3" {
    bucket         = "et-devops-tools"
    region         = "us-east-1"
    dynamodb_table = "et-devops-terraform-state-lock"
    acl            = "private"
    key            = "terraform/<explicitly_state_your_acct_id>/<explicitly_state_your_region>/terraform.tfstate"
    encrypt        = "true"
    profile        = "evertruetools"
  }
}
