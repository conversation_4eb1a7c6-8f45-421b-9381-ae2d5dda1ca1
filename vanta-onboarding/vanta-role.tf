resource "aws_iam_role" "vanta_auditor" {
  name               = "vanta-auditor"
  description        = "Vanta auditor role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json

  tags = {
    terraform = "Managed by Terraform"
  }
}

data "aws_iam_policy_document" "assume_role_policy" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${var.aws_account_id}:root"]
    }

    condition {
      test     = "StringEquals"
      variable = "sts:ExternalId"

      values = [
        var.external_id
      ]
    }
  }
}

resource "aws_iam_role_policy_attachment" "attach_security_audit" {
  role       = aws_iam_role.vanta_auditor.name
  policy_arn = "arn:aws:iam::aws:policy/SecurityAudit"
}

resource "aws_iam_role_policy_attachment" "attach_vanta_additional_permissions" {
  role       = aws_iam_role.vanta_auditor.name
  policy_arn = "arn:aws:iam::************:policy/VantaAdditionalPermissions"
}